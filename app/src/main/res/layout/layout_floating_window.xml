<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!-- 悬浮窗主体内容 -->
    <LinearLayout
        android:id="@+id/layout_main"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_floating"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 上半部分：倒计时显示区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="start|center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:background="@drawable/ic_timer" />

                <TextView
                    android:id="@+id/tv_countdown"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:text="00:00"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- 下半部分：主页按钮 -->
            <LinearLayout
                android:id="@+id/btn_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start|center_vertical">

                <View
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:background="@drawable/ic_floating_home" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:text="返回主页"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </LinearLayout>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/btn_arrow"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="20dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="match_parent"
                android:background="@android:color/transparent"
                android:contentDescription="展开/收起悬浮窗"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_arrow_white" />

        </FrameLayout>
        <!-- 右侧箭头按钮 -->

    </LinearLayout>

    <!-- 警告显示区域 - 放在悬浮框下面，使用独立定位 -->
    <LinearLayout
        android:id="@+id/layout_warning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start|top"
        android:layout_marginStart="8dp"
        android:layout_marginTop="80dp"
        android:background="@drawable/warning_background"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:visibility="gone"
        tool:visibility="visible">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_lock" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/governance_warning"
            android:textColor="@color/lock_accent_orange"
            android:textSize="13sp"
            android:textStyle="bold" />

    </LinearLayout>

</FrameLayout>
